// pages/login/index.js
const request = require('../../utils/request.js');
const toastDialog = require('../../utils/toastDialog.js')
const app = getApp()
var that;
Page({
  /**
   * 页面的初始数据
   */
  data: {
    agree: false,
    checkboxagree: false,
    projectId:0
  },
  checkboxChange(e) {
    that.setData({
      checkboxagree: !that.data.checkboxagree, // 更新 checkbox 的选中状态
    });
  },
  //《隐私政策》
  privacypolicy() {
    wx.navigateTo({
      url: '/pages/login/privacypolicy/privacypolicy',
    })
  },
  //《用户协议》
  agree() {
    wx.navigateTo({
      url: '/pages/login/agreement/agreement',
    })
  },
  submit(e) {   
    if (e.detail.iv == undefined) {
    wx.navigateTo({
      url: '/pages/index/index',
    }) 
   }else{
    wx.login({
      success: res => {
        var data = {
          loginCode: res.code,
          phoneCode: e.detail.code,
          projectId: that.data.projectId
        }
        request.post({
          url: 'SysUser/OCLogin',
          data,
          loadmsg:"登录中"
        })
        .then(res => {
          app.globalData.token = res.data.token
          toastDialog.TSuccess("登录成功",()=>{
                wx.navigateTo({
                  url: '/pages/index/index',
                })   
           })
        })
      }
    })
  }
  },
  back(){
    wx.navigateBack({
      delta: 1
    });
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    that=this
    that.setData({
      projectId: options.projectId,
    })
    // wx.login({
    //   success: function (res) {
    //     request.get({
    //         url: 'SysUser/getsession_key',
    //         data: {
    //           code: res.code
    //         }
    //       })
    //       .then(data => {
    //         that.setData({
    //           session_key: data.data.session_key,
    //         })
    //       })
    //   }
    // })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})