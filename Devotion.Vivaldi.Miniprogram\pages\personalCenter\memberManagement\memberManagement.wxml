 <view class="page-view">
    <view class="page-head flex align-center">
      <view class="item-c title">成员管理</view>
      <view class="item-r"></view>
    </view>
    <view class="group-rows">
      <van-cell-group inset custom-class="van-cell-group_custom" bindtap="editFamily">
        <van-cell title="项目名称" is-link value="{{projectName}}" />
      </van-cell-group>
    </view>
    <view class="group-rows">
      <van-cell-group inset custom-class="van-cell-group_custom">
        <van-cell title="成员（{{persons.length}}）">
          <button class="btn-transparent" style="text-align: right;" open-type='share' hover-class="none"> <van-icon slot="right-icon" name="add-o" size="50rpx" color="#4095E5"  /></button>
        </van-cell>
        <van-cell center wx:for="{{persons}}" wx:key="index">
          <view slot="title">
            <view class="">{{item.name}}</view>
            <view class="">{{item.mobile}}</view>
          </view>
          <view slot="icon">
            <image mode="aspectFit" class="avatar" src="{{item.avatarUrl}}"></image>
          </view>
          <view use-label-slot="label" wx:if="{{item.type==0}}"><view class="van-cell-text">管理员</view></view>          
          <van-icon wx:if="{{isadmin&&item.id!=userInfo.id&&item.type!=0}}" slot="right-icon" name="close" size="40rpx" color="#EE5151" data-id="{{item.id}}" bindtap="delPerson" />
        </van-cell>
      </van-cell-group>
    </view>
    <!-- <view class="padding-lr-50" wx:if="{{isadmin}}">
      <van-button round type="info" block bindtap="onShowPicker">转移管理员</van-button>
    </view>
    <view class="padding-lr-50 padding-top-10" wx:if="{{isadmin}}">
      <van-button round type="danger"  block bindtap="delFamily" >删除家庭</van-button>
    </view>
    <view class="padding-lr-50 padding-top-10" wx:if="{{!isadmin}}">
      <van-button round type="danger"  block bindtap="outputFamily" >退出家庭</van-button>
    </view> -->
  </view>
<van-toast id="van-toast" />
<van-dialog id="van-dialog" />
<van-popup show="{{ showPicker }}"  bind:close="onClosePicker" position="bottom" round custom-style="height: 80%">
  <van-empty wx:if="{{pickerPersons.length==0}}" description="无其他成员信息" />
  <van-picker  wx:if="{{pickerPersons.length>0}}" show-toolbar="true" title="请选择管理员" columns="{{ pickerPersons }}" bind:change="onChange" bind:cancel="onClosePicker" bind:confirm="tranfAdmin" />
</van-popup>