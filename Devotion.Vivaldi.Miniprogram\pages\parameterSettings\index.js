// pages/parameterSettings/index.js
const request = require('../../utils/request.js');
const toastDialog = require('../../utils/toastDialog.js')
const app = getApp();
var that;
Page({

  /**
   * 页面的初始数据
   */
  data: {
    id:0,
    roomname:'',
    projectId:0,
    projectName:'',
    info:null,
    setTemperature:0,
    setHumidity:0,
    timeVisible:false,
    ontimeVisibleindex:0,
    timeVisibletype:0,
    currentDate: '',

  },
//根据房间传感器Id获取信息
  getSensorConfigSettingById(){
    request.get({
      url: 'SensorConfig/GetSensorConfigSettingById',
      data: {
        id:that.data.id
      }
    })
    .then(res => {
         that.setData({
          info:res.data,
          setTemperature:res.data.setTemperature,
          setHumidity:res.data.setHumidity
         })
      })
  },
  onslider(e) {
    var val = e.detail;
    var info = that.data.info
    info.setTemperature=val
    that.setData({
      info:info
    })
  },
  // ondrag(e) {
  //   var val = e.detail;
  //   var info = that.data.info
  //   info.setTemperature=val
  //   that.setData({
  //     info:info
  //   })
  // },
  onslider1(e) {
    var val = e.detail;
    var info = that.data.info
    info.setHumidity=val
    that.setData({
      info:info
    })
  },

  onValveStatusChange(event) {
    const newValue = event.detail; // 用户选择的值（字符串类型）
    const index = event.currentTarget.dataset.index; // 当前项的索引
    // 更新数据（确保转为 int）
    const path = `info.thermoelectricValve[${index}].setThermalValveStatus`;
    this.setData({
      [path]: parseInt(newValue) //
    });
  },
  onValveStatusChange1(event) {
    const newValue = event.detail; // 用户选择的值（字符串类型）
    const index = event.currentTarget.dataset.index; // 当前项的索引
    // 更新数据（确保转为 int）
    const path = `info.airValve[${index}].setAirValveStatus`;
    this.setData({
      [path]: parseInt(newValue) //
    });
  },
  onConfirmClick(){
    toastDialog.DialogConfirm("确定要保存吗？", () => {
         var info= that.data.info
         request.post({
          url: 'SensorConfig/SetMiniSave',
          data: info
        })
        .then(res => {
          toastDialog.TSuccess("保存成功", () => {
                
              
              })
          })
    })
  },
  ontimeVisible(event){
    const index = event.currentTarget.dataset.index; // 当前项的索引
    that.setData({
      timeVisible:true,
      timeVisibletype:0,
      ontimeVisibleindex:index,
      currentDate:  event._relatedInfo.anchorTargetText,
    })
  },
  offtimeVisible(event){
    const index = event.currentTarget.dataset.index; // 当前项的索引
    that.setData({
      timeVisible:true,
      timeVisibletype:1,
      ontimeVisibleindex:index,
      currentDate:  event._relatedInfo.anchorTargetText,
    })
  },
  timeVisibleclos(){
    that.setData({
      timeVisible:false
    })
  },
  timeVisibleconfirm(e){
   const currentDate= e.detail
   const timeVisibletype=  that.data.timeVisibletype
   const ontimeVisibleindex=  that.data.ontimeVisibleindex
   switch (timeVisibletype) {
       case 0:
        const path1 = `info.airValve[${ontimeVisibleindex}].onAirValveTime`;
        this.setData({
          [path1]: currentDate,
          timeVisible:false

        });
       break;
       case 1:
        const path = `info.airValve[${ontimeVisibleindex}].offAirValveTime`;
        this.setData({
          [path]: currentDate,
          timeVisible:false
        });
       break;
   }
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    that=this;
    that.setData({
      roomname: options.roomname,
      id: options.id
    })
   
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    that=this;
    that.setData({
      projectName:wx.getStorageSync('projectName'),
      projectId:wx.getStorageSync('clickProjectId')
   })
   that.getSensorConfigSettingById()
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})