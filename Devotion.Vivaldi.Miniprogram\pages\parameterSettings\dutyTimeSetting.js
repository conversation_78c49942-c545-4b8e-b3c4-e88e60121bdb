// index.js
Page({
  data: {
    days: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
    activeDay: 0,
    schedule: {},
    timeSlots: []
  },

  onLoad() {
    // 初始化时间槽
    const timeSlots = Array.from({length: 48}, (_, i) => {
      const hours = Math.floor(i / 2).toString().padStart(2, '0')
      const minutes = (i % 2 === 0 ? '00' : '30')
      return `${hours}:${minutes}`
    })
    
    // 初始化排班数据
    const schedule = {}
    this.data.days.forEach(day => {
      schedule[day] = Array(48).fill(false)
    })

    this.setData({
      timeSlots,
      schedule
    })
  },

  switchDay(e) {
    const dayIndex = e.currentTarget.dataset.day
    this.setData({ activeDay: dayIndex })
  },

  toggleTime(e) {
    const index = e.currentTarget.dataset.index
    const currentDay = this.data.days[this.data.activeDay]
    const path = `schedule.${currentDay}[${index}]`
    
    this.setData({
      [path]: !this.data.schedule[currentDay][index]
    })
  }
})