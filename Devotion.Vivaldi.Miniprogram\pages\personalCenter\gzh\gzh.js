const request = require('../../../utils/request.js');
const toastDialog = require('../../../utils/toastDialog.js')
var QRCode = require('../../../utils/weapp-qrcode.js')
const app = getApp();
var that;
Page({

  /**
   * 页面的初始数据
   */
  data: {
     id:0,
     loading:true,
     qrcode_src:''
  },
  init(){
    request.get({
      url: 'SysUser/GetGzhQrcode',
      data:{id:that.data.id}
    })
    .then(res => {
      that.setData({
        loading:false
      })
      var qrcode = new QRCode('canvas', {
        // usingIn: this,
        text: res.data,
        width: 250,
        height: 250,
        padding: 12,
        colorDark: "#000000",
        colorLight: "#ffffff",
        correctLevel: QRCode.CorrectLevel.H,
        callback: (res) => {
            // 生成二维码的临时文件
            that.setData({
        
              qrcode_src:res.path
          
            })
         
        }
      });
    })
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    that=this
    that.setData({
      id:options.id
    })
    that.init()

  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  
})