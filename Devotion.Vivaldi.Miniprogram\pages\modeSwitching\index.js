// pages/modeSwitching/index.js
const request = require('../../utils/request.js');
const toastDialog = require('../../utils/toastDialog.js')
const app = getApp();
var that;
Page({
  /**
   * 页面的初始数据
   */
  data: {
    isnew:false,
     projectId:0,
     projectName:'',
     yxmodel:'',
     systatus:'',
     projectOverallSettings:null,
     redisProjectInfo:null,
     automaticSeason:null,
     dutyTimeSetting:null,
   // 日期选择器配置
   selectedMonth: 1,
   selectedDay: 1,
   showPicker: false,
     columns: [
      { values: [1,2,3,4,5,6,7,8,9,10,11,12], className: 'm' },
      { values: [1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31], className: 'd' }
    ],
      // 当前编辑的日期字段（区分季节类型）
    currentEditType: '',
    currentDate: [{
      values: [2]
    },
    {
      values: [2]
    }],
  },

  getMiniByProjectId(){
    request.get({
      url: 'ProjectOverallSettings/getMiniByProjectId',
      data: {
        projectId:that.data.projectId
      }
    })
    .then(res => {
     var projectOverallSettings= res.data.projectOverallSettings
     var automaticSeason= res.data.automaticSeason
     var dutyTimeSetting= res.data.dutyTimeSetting
     var yxmodel =  projectOverallSettings.seasonMode == 0 ? '手动' : '自动'
     var systatus =    projectOverallSettings.workingCondition == 0
     ? '关闭'
     : projectOverallSettings.workingCondition == 1
       ? '运行'
       : projectOverallSettings.workingCondition == 2
         ? projectOverallSettings.automaticWorking == 1
           ? '值班'
           : projectOverallSettings.automaticWorking == 2
             ? '值班'
             : '未知'
         : '未知'
      that.setData({
        projectOverallSettings: projectOverallSettings,
        automaticSeason:automaticSeason,
        dutyTimeSetting:dutyTimeSetting,
        redisProjectInfo:res.data.redisProjectInfo,
        yxmodel,
        systatus
      })
    })
  },
  onSeasonalPatternPicker(){
    that.setData({
      seasonalPatternPicker:true,

    })
  },
  //确认季节模式
  onSeasonalPatternChange(e){
    const {value,label}=e.detail
    that.setData({
        seasonalPatternText: label,
        seasonalPatternVal:value
    });
  },
  onSeasonModeChange(e){
    var seasonMode=  e.detail
    var projectOverallSettings= that.data.projectOverallSettings
    projectOverallSettings.seasonMode=seasonMode
    that.setData({
      projectOverallSettings: projectOverallSettings
  });
  },
  // 手动模式下的运行模式切换
  onOperatingModeChange(e){
    var operatingMode = e.detail
    var projectOverallSettings = that.data.projectOverallSettings
    projectOverallSettings.operatingMode = operatingMode
    that.setData({
      projectOverallSettings: projectOverallSettings
    });
  },
  onWorkingCondition(e){
    var workingCondition=  e.detail
    var projectOverallSettings= that.data.projectOverallSettings
    projectOverallSettings.workingCondition=workingCondition
    that.setData({
      projectOverallSettings: projectOverallSettings
  });
  },
  onAutomaticWorking(e){
    var automaticWorking=  e.detail
    var projectOverallSettings= that.data.projectOverallSettings
    projectOverallSettings.automaticWorking=automaticWorking
    that.setData({
      projectOverallSettings: projectOverallSettings
  });
  },

  // 点击日期项
  timeck(e) {
    const type = e.currentTarget.dataset.type; // c1/c2 对应不同季节字段
    const val = e.currentTarget.dataset.val; // c1/c2 对应不同季节字段
    const currentDate = this.parseDateToValues(val);

    this.setData({
      showPicker: true,
      currentEditType: type
    });
  },
   // 日期选择确认
   onConfirm(e) {
    const [month, day] = e.detail.value;
    const dateStr =`${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`;

    let targetKey = '';
    switch (this.data.currentEditType) {
      case 'c1': targetKey = 'springStartTime'; break;
      case 'c2': targetKey = 'springEndTime'; break;
      case 'c3': targetKey = 'summerStartTime'; break;
      case 'c4': targetKey = 'summerEndTime'; break;
      case 'c5': targetKey = 'autumnStartTime'; break;
      case 'c6': targetKey = 'autumnEndTime'; break;
      case 'c7': targetKey = 'winterStartTime'; break;
      case 'c8': targetKey = 'winterEndTime'; break;
    }

    this.setData({
      [`automaticSeason.${targetKey}`]: dateStr,
      showPicker: false
    });
  },
    onCancel(){
      that.setData({
        showPicker:false,
   })
    },
    parseDateToValues(dateStr) {

      const match = dateStr.split("-")

      return match ? [parseInt(match[0]), parseInt(match[1])] : [1, 1];
    },
    // 根据月份生成对应日期
  updateDays(month) {
    const daysInMonth = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];
    const maxDay = daysInMonth[month - 1];
    return Array.from({ length: maxDay }, (_, i) => i + 1);
  },

  // 列值变化时触发
  onPickerChange(e) {
    const { picker, value } = e.detail;
    const [month, day] = value;

    if (picker.column === 0) {
      const newDays = this.updateDays(month);
      this.setData({
        ['columns[1].values']: newDays
      });
    }
  },
 convertMMDDToTimestamp(dateString, year = new Date().getFullYear()) {
      const [monthStr, dayStr] = dateString.split("-");
      const month = parseInt(monthStr, 10) - 1;
      const day = parseInt(dayStr, 10);

      const date = new Date(year, month, day);

      // 验证日期有效性
      if (date.getMonth() !== month || date.getDate() !== day) {
        throw new Error("Invalid date: " + dateString);
      }

      return date.getTime();
    },
    save(){
      toastDialog.DialogConfirm("确定要保存吗？", () => {
        var info= that.data.projectOverallSettings
        var automaticSeason= that.data.automaticSeason
        info.springStartTime= automaticSeason.springStartTime
        info.springEndTime= automaticSeason.springEndTime
        info.summerStartTime= automaticSeason.summerStartTime
        info.summerEndTime= automaticSeason.summerEndTime
        info.autumnStartTime= automaticSeason.autumnStartTime
        info.autumnEndTime= automaticSeason.autumnEndTime
        info.winterStartTime= automaticSeason.winterStartTime
        info.winterEndTime= automaticSeason.winterEndTime

        request.post({
         url: 'ProjectOverallSettings/Update',
         data: info
       })
       .then(res => {
         toastDialog.TSuccess("保存成功", () => {


             })
         })
   })
    },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    that=this

  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },
  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    that=this
    that.setData({
       projectName:wx.getStorageSync('projectName'),
       projectId:wx.getStorageSync('clickProjectId'),
       isnew:app.globalData.isnew,
    })

    if(!that.data.isnew){

      that.getMiniByProjectId()
    }
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})