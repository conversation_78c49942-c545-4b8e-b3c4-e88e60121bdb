/* index.wxss */
.day-tabs {
  white-space: nowrap;
  padding: 20rpx 0;
  background: #f8f8f8;
  border-bottom: 1rpx solid #eee;
}

.tab {
  display: inline-block;
  padding: 20rpx 40rpx;
  margin: 0 10rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  transition: all 0.3s;
}

.tab.active {
  background: #007aff;
  color: white;
}

.time-container {
  padding: 30rpx;
}

.time-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 10rpx;
}

.time-slot {
  width: 140rpx;
  height: 60rpx;
  line-height: 60rpx;
  text-align: center;
  border: 2rpx solid #ddd;
  border-radius: 8rpx;
  font-size: 24rpx;
  color: #666;
  transition: all 0.2s;
}

.time-slot.selected {
  background: #007aff;
  color: white;
  border-color: #007aff;
}