// index.js
const toastDialog = require('../../utils/toastDialog.js')
var userInfoAny = require('../../utils/userInfoAny.js');
const app = getApp()
var that;
Page({
  data: {
    motto: 'Hello World',
  },
    /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    that=this
    // wx.reLaunch({
    //   url: '/pages/home/<USER>'
    // })
    // toastDialog.showAlertDialog("大大大大是",()=>{
    //   console.log(2222222)
    // })
    that.userIsAny()
  },
  userIsAny(){
    userInfoAny.getUserInfoAny(res=>{
      if(res.data.id!=0){
        app.globalData.isnew=false
      }else{
        app.globalData.isnew=true
      }
      wx.reLaunch({
        url: '/pages/home/<USER>',
      })
    },err=>{

    })
  }
})
