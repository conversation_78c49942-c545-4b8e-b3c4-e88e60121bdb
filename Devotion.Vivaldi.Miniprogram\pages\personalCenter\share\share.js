// pages/personalCenter/share/share.js
const request = require('../../../utils/request.js');
var userInfoAny = require('../../../utils/userInfoAny.js');
const toastDialog = require('../../../utils/toastDialog.js')
const app = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    imageUrl: app.globalData.imageUrl,
    //判断小程序的API，回调，参数，组件等是否在当前版本可用。
    canIUse: wx.canIUse('button.open-type.getUserInfo'),
    projectId: 0,
    name: '',
  },
  BindNow: function () {
    var that = this
    that.UserIsAny()
  },
  UserIsAny: function () {
    var that = this
    var projectId = that.data.projectId;
    userInfoAny.getUserInfoAny(res=>{
      if(res.data.id!=0){
        request.get({
          url:"SysUser/UserAdd",
          data:{projectId}
       }).then((res) => {
           wx.showToast({
             title: '加入成功',
           })
           wx.reLaunch({
             url: '/pages/home/<USER>',
           })
       })
      }else{
        toastDialog.DialogAlert("欢迎您新用户，请前往登录。",()=>{
          wx.reLaunch({
            url: '/pages/login/login?projectId='+projectId
          })
     })

    }
    },err=>{
      wx.reLaunch({
        url: '/pages/login/login?projectId='+projectId
      })
    })
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.setData({
      projectId: option.projectId,
      name: option.name
    })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})