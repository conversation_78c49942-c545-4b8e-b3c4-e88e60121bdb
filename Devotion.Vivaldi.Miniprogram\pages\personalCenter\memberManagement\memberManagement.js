// pages/personalCenter/memberManagement/memberManagement.js
const request = require('../../../utils/request.js');
const toastDialog = require('../../../utils/toastDialog.js')
const app = getApp();
var that;
Page({

  /**
   * 页面的初始数据
   */
  data: {
    userInfo: null,
    projectId:0,
    projectName:'',
    isadmin: false,
    persons:[]
  },
  getSysUserinfo(){
    that.setData({
      isadmin: false
    })
    var userId = that.data.userInfo.id
    request.get({
      url: 'SysUser/GetSysUserinfo',
      data: {
        projectId:that.data.projectId
      }
    })
    .then(res => {
      
         that.setData({
          persons:res.data
         })
      })
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    that=this
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    that=this
    that.setData({
      projectName:wx.getStorageSync('projectName'),
      projectId:wx.getStorageSync('clickProjectId'),
      userInfo:wx.getStorageSync('userInfo')
   })
   that.getSysUserinfo()
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    var nickName = that.data.userInfo.name
    return {
      //## 此为转发页面所显示的标题
      title: nickName + '邀请你成为成员',
      //## 此为转发页面的描述性文字
      //desc: '邀请你成为家庭成员!', 
      //## 此为转发给微信好友或微信群后，对方点击后进入的页面链接，可以根据自己的需求添加参数
      path: '/pages/personalCenter/share/share?projectId=' + this.data.projectId + "&name=" + nickName,
      imageUrl: app.globalData.imageUrl + '/imgs/logo.png',
      //## 转发操作成功后的回调函数，用于对发起者的提示语句或其他逻辑处理
      success: function (res) {
        //这是我自定义的函数，可替换自己的操作
        util.showToast(1, '发送成功');
      },
      //## 转发操作失败/取消 后的回调处理，一般是个提示语句即可
      fail: function () {
        util.showToast(0, '...');
      }
    }
  }
})