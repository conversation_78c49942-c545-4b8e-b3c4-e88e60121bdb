
page {
  background-color: #EEF1F5;
  padding-top: 50rpx;
 
}
:root{
  --van-cell-group-inset-padding:100rpx 0;
  --van-cell-icon-size:50rpx;
}
.headerbox {
  padding: 0rpx 10rpx 60rpx 30rpx;
  margin: 0 auto;
  width: 95%;
  line-height: 20px;
  border-radius: 10px;
  text-align: center;
  display: flex;
  align-items: center;
  font-size: 30rpx;
}
.avata {
  background-color: transparent;
  padding: 10px 5px 10px 5px;
}
button::after {
  border: none;
}

.van-cell-group_custom{
  padding: 50rpx 0; 
  background-color: white;
}
.avatabtn{
  background-color: transparent;
  text-align: left;
  padding-left: 0px;
  padding-right: 0px;
}
.van-cell--large {
   padding: 40rpx 40rpx !important;
}
.van-cell-group_custom {
  padding: 20rpx 0 !important
}