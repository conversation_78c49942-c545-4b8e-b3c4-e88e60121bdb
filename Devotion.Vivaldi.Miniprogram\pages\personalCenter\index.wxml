<!--pages/personalCenter/index.wxml-->
<view class="page-view">
  <view class="headerbox">
    <view class="padding-top-10">
      <button wx:if="{{isnew}}" class="avata">
       <image  src="/assets/imgs/tx.png" style="width: 70px; height: 70px; border-radius: 50%;"></image>
      </button>
      <button wx:else class="avata" open-type="chooseAvatar" bind:chooseavatar="onChooseAvatar">
        <!-- <image  src="/assets/imgs/tx.png" style="width: 50px; height: 50px; border-radius: 50%;"></image> -->
        <image  src="{{userInfo.avatarUrl}}" style="width: 60px; height: 60px; border-radius: 50%;"></image>
      </button>
    </view>
    <view class="text-align-left padding-left-10">
      <view wx:if="{{isnew}}"  bindtap="newlogin">未登录</view>
      <view wx:else>
        <view class="text-font-18">{{userInfo.name!=null?userInfo.name:''}}</view>
        <view class="text-font-16 padding-top-10">{{userInfo.mobile}}</view>
      </view>
    </view>
  </view>
  <van-cell-group inset custom-class="van-cell-group_custom">
    <view class="">
    <van-cell size=""  class="van-cell_custom" title="成员管理" is-link bindtap="memberManagement">
      <van-icon slot="icon" name="wap-home-o" class="padding-right-10" size="40rpx" color="#1989fa"  />
    </van-cell>
  </view>
   <view class=" ">
    <van-cell size=""  class="van-cell_custom" title="常见问题" is-link >
      <van-icon slot="icon" name="question-o" class="padding-right-10" size="40rpx" color="#1989fa"  />
    </van-cell>
   </view>
   <!-- <view class=" ">
    <van-cell size=""  class="van-cell_custom" title="售后服务" is-link >
      <van-icon slot="icon" name="phone-o" class="padding-right-10" size="40rpx" color="#1989fa"  />
    </van-cell>
   </view> -->
  <view class="">
    <button  class="avatabtn" open-type="feedback">
    <van-cell size=""  title="意见反馈" is-link>
      <van-icon slot="icon" name="notes-o" class="padding-right-10" size="40rpx" color="#1296DB" />
    </van-cell>
  </button>
  </view>
  <view class="">
    <van-cell size="" title="关于我们" is-link bindtap="about">
      <van-icon slot="icon" name="info-o" class="padding-right-10" size="40rpx" color="#1989fa" />
    </van-cell>
  </view>
  <view class=" ">
    <van-cell size=""  class="van-cell_custom" title="消息通知" is-link bindtap="gzh" label="绑定微信公众号" >
      <view>
        <text wx:if="{{userInfo.isBdGzh}}" class="text-color-success">已绑定</text>
        <text wx:else class="text-color-red">未绑定</text>
      </view>
      <van-icon slot="icon" name="comment-o" class="padding-right-10" size="40rpx" color="#1989fa"  />
    </van-cell>
   </view>
  <view class="">
    <van-cell size="" title="退出" is-link bindtap="out">
      <van-icon slot="icon" name="list-switching" class="padding-right-10" size="40rpx" color="#1989fa" />
    </van-cell>
  </view>
  </van-cell-group>
</view>
<van-toast id="van-toast" />
<van-dialog id="van-dialog" />
