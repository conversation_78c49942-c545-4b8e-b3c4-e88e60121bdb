<!--pages/home/<USER>
<view wx:if="{{!isnew}}">
  <van-sticky>
<view class="top" >
    <view class="swiperimg-view-home">
        <view class="text-color-primary text-font-bold padding-left-10 text-font-18">Vivaldi</view>
    </view>
    <view>
    <view class="box" bindtap="cutfamily">
        <view class="flex-item text-font-16" style="padding:0 5px 0 10px;line-height: 32rpx;">{{projectName}}</view>
        <view class=""><van-icon name="arrow" /></view>
    </view>
    <!-- 楼层分类 -->
    <view class="menu">
      <view class="menu_pa shadow">
        <view wx:for="{{floorlist}}" wx:key="index" id="{{item.floor}}" class="{{clickFloor == item.floor?'color_onClick':'color_onBlur'}} menu_item" bindtap="deviceListByfloor">{{item.floorName}}</view>
      </view>
    </view>
   </view>
  </view>
  </van-sticky>
  <!-- 房间 -->
  <view class="padding-left-10 padding-right-10">
    <view class="room-page clearfix">
      <view class="room-page-view"  wx:for="{{roomlist}}" data-roomname="{{item.roomName}}"  data-id="{{item.sensorConfigId}}" wx:key="index"  catchtap="goDetail" >
       <view class="room-page-column">
          <view class="flex-row text-align-center">
            <view class="flex-item padding-left-10">
               <image src="{{item.icon}}" style="width:35px;height:35px"></image>
               </view>
            <view class="flex-item padding-right-10">
                <!-- <view class="text-color-primary  text-font-11">负一楼</view> -->
                <view class="text-font-14">{{item.roomName}}</view>
            </view>
          </view>
          <view class="margin-top-10 flex-row text-color-content text-font-11">
            <view class="flex-item text-align-center padding-left-10">
               <view>{{item.currentTemperature}}℃</view>
               <view>温度</view>
            </view>
            <view class="flex-item text-align-center padding-right-10">
              <view>{{item.currentHumidity}}%</view>
               <view>湿度</view>
            </view>
          </view>
           <view class="floorclas text-color-primary  text-font-10">{{item.floorName}}</view>
        </view>
      </view>
    </view>
  </view>
  
</view>
<view wx:else>
  <view class="top" >
    <view class="swiperimg-view-home">
          <view class="text-color-primary text-font-bold padding-left-10 text-font-18">Vivaldi</view>
          <view class=" text-font-bold padding-top-20 padding-bottom-10 padding-left-10">我的家</view>
      </view>
  </view>
  <view class="padding-lr-20"> 
    <view class="newview"> 
       <view class="text-color-primary text-font-bold padding-lr-50 text-font-40">Vivaldi</view>
       <view class="text-color-textgray padding-lr-40 padding-top-20 text-font-18">以时代引领者的姿态</view>
       <view class="text-color-textgray padding-lr-40 text-font-18">站在室内人居环境的世界前沿</view>
       <view class="margin-top-50" style="padding: 0 200rpx;">
        <van-button type="info" round block bind:click="login">登录</van-button>
       </view>
   
    </view>
  </view>
</view>
<van-popup show="{{projectVisible}}" position="bottom" round custom-style="height:40%;" bind:close="onClose">
  <view class="padding-10">
    选择项目:
  </view>
  <van-cell wx:for="{{projectlist}}" wx:key="index" title="{{item.projectName}}" size="large" data-id="{{item.id}}"  data-projectname="{{item.projectName}}" clickable bind:click="onProjectConfirm">
    <van-icon wx:if="{{clickProjectId==item.id}}" slot="right-icon" name="success" color="#0081ff" />
  </van-cell>
</van-popup>
<van-toast id="van-toast" />
<van-dialog id="van-dialog" />
