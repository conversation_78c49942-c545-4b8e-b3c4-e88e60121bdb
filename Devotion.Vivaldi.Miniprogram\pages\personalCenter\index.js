// pages/personalCenter/index.js
const request = require('../../utils/request.js');
const toastDialog = require('../../utils/toastDialog.js')
const app = getApp();
var that
Page({

  /**
   * 页面的初始数据
   */
  data: {
    userInfo:null,
    imageUrl: 'https://tdesign.gtimg.com/mobile/demos/avatar1.png',
    isnew:false, //判断是否新用户
  },
      //新用户登录
 newlogin() {
        wx.navigateTo({
          url: '/pages/login/index?projectId=0'
        })
    },
  about(){
    wx.navigateTo({
      url: '/pages/personalCenter/about/about',
    })
  },
  onChooseAvatar(e) {
    const { avatarUrl } = e.detail 
    let sBase64 =  wx.getFileSystemManager().readFileSync(avatarUrl, "base64")
    request.post({
      url: 'sysuser/SaveAvatarUrl',
      data: {
         Avatarbase:sBase64
      }
    })
    .then(res => {
      var userInfo= that.data.userInfo
      userInfo.avatarUrl=res.data
      that.setData({
        userInfo
      })
      wx.setStorage({
        key: "userInfo",
        data:userInfo
      })
    })
  },
   //个人信息
   message(){
    if(!that.data.isnew){
      wx.navigateTo({
        url: '/pages/personalCenter/message/message?id='+that.data.userInfo.id,
      })
    }else{
      that.newlogin()
    }
  },
  memberManagement(){
    if(!that.data.isnew){
      wx.navigateTo({
        url: '/pages/personalCenter/memberManagement/memberManagement',
      })
    }else{
      that.newlogin()
    }
  },

  

  out(){
    toastDialog.DialogConfirm("确认退出吗？",()=>{
      request.get({
        url: 'SysUser/UserOut'
      })
      .then(res => {
        toastDialog.TSuccess("退出成功",()=>{
          wx.reLaunch({
            url: '/pages/login/login' //跳转
          })
        })
      })
    })
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    that=this;
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    that=this;
    var userInfo= wx.getStorageSync('userInfo')
    that.setData({
      userInfo,
      isnew:app.globalData.isnew
    })
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },
  gzh(){
    if(!that.data.isnew){
     var userInfo = that.data.userInfo
     if(userInfo.isBdGzh){
       toastDialog.DialogConfirm("确认解绑维瓦尔第五恒空调公众号？",()=>{
        request.get({
          url: "SysUser/UnBindingGzh",
          data:{id:userInfo.id}
        }).then(res => {
          toastDialog.TSuccess("成功",()=>{
        
                  userInfo.isBdGzh=false
                  that.setData({
                    userInfo:userInfo
                  })
                  wx.setStorage({
                    key: "userInfo",
                    data:userInfo
                  })
                })
          })
        })
     }else{
      toastDialog.DialogConfirm("确认绑定维瓦尔第五恒空调公众号？",()=>{
        request.get({
          url: "SysUser/BindingGzh",
          data:{id:userInfo.id}
        }).then(res => {
          if(res.data!=null){
            toastDialog.TSuccess("成功",()=>{
                  userInfo.gzhOpenid=res.data
                  userInfo.isBdGzh=true
                  userInfo.isGzGzh=true
                  that.setData({
                    userInfo:userInfo
                  })
                  wx.setStorage({
                    key: "userInfo",
                    data:userInfo
                  })
                })
              }else{
                wx.navigateTo({
                  url: '/pages/personalCenter/gzh/gzh?id='+userInfo.id,
                })
              }
          })
        })
     }
 
    }else{
      that.newlogin()
    }
  },
})