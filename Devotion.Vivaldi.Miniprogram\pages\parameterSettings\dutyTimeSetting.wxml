<!--pages/parameterSettings/dutyTimeSetting.wxml-->
<!-- index.wxml -->
<scroll-view scroll-x class="day-tabs">
  <view 
    wx:for="{{days}}"
    wx:key="index"
    class="tab {{activeDay === index ? 'active' : ''}}"
    bindtap="switchDay"
    data-day="{{index}}"
  >
    {{item}}
  </view>
</scroll-view>

<view class="time-container">
  <view class="time-grid">
    <view 
      wx:for="{{timeSlots}}"
      wx:key="index"
      class="time-slot {{schedule[days[activeDay]][index] ? 'selected' : ''}}"
      bindtap="toggleTime"
      data-index="{{index}}"
    >
      {{item}}
    </view>
  </view>
</view>