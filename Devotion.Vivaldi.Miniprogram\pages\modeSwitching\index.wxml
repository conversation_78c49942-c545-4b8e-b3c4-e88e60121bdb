<!--pages/modeSwitching/index.wxml-->
<view class="padding-lr-10">
  <view wx:if="{{!isnew}}">
  <view class="padding-20 text-color-primary text-font-bold text-font-18">{{projectName}}</view>

  <view class="text-align-center text-color-black  text-font-bold padding-bottom-10">设备运行情况</view>

  <van-cell-group inset>
    <van-cell title="运行模式" value="{{yxmodel}}" />
    <van-cell title="系统状态" value="{{systatus}}" />
    <van-cell title="工作模式">
      <view>
        <view wx:if="{{projectOverallSettings.operatingMode== 0}}">供冷</view>
        <view wx:if="{{projectOverallSettings.operatingMode== 1}}">供暖</view>
        <view wx:if="{{projectOverallSettings.operatingMode== 2}}">通风</view>
      </view>
    </van-cell>
    <van-cell title="室外温度" value="{{projectOverallSettings.outdoorTemperature}}°C" />
    <van-cell title="室外湿度" value="{{projectOverallSettings.outdoorHumidity}}%" />
    <van-cell title="网络状态"  >
      <text class="text-color-success" wx:if="{{redisProjectInfo.online}}" >在线</text>
      <text  class="text-color-red"  wx:else>离线</text>
    </van-cell>
    <van-cell wx:if="{{!redisProjectInfo.online}}" value-class="text-color-red" title="离线时间" value="{{projectOverallSettings.updateTime}}" />
    <van-cell title="报警级别" value="" />
  </van-cell-group>

  <view class="text-align-center text-color-black  text-font-bold  padding-tb-10">设备运行设置</view>

  <van-cell-group inset>
    <van-cell title="季节模式">
      <van-radio-group value="{{''+projectOverallSettings.seasonMode}}" direction="horizontal" bind:change="onSeasonModeChange">
        <van-radio name="0">手动</van-radio>
        <van-radio name="1">自动</van-radio>
      </van-radio-group>
    </van-cell>
    <view class="margin-tb-10 text-font-14 padding-tb-10 bg-color-white " wx:if="{{projectOverallSettings.seasonMode==1}}">
      <view class="text-font-14 padding-10 ">
        <view class="padding-left-5">春季供暖</view>
        <view class="flex-row padding-top-10 padding-left-5">
          <view class="flex-item text-color-content" data-type="c1" data-val="{{automaticSeason.springStartTime}}" bindtap="timeck">
            开始日期：{{automaticSeason.springStartTime}}
            <van-icon name="arrow" />
          </view>
          <view class="flex-item text-color-content" data-type="c2" data-val="{{automaticSeason.springEndTime}}" bindtap="timeck">
            结束日期：{{automaticSeason.springEndTime}}
            <van-icon name="arrow" />
          </view>
        </view>
      </view>

      <!-- 分割线 -->
      <view style="border: 1px solid #f0f0f0;"></view>

      <!-- 夏季 -->
      <view class="padding-10">
        <view class="padding-left-5">夏季</view>
        <view class="flex-row padding-top-10 padding-left-5">
          <view class="flex-item text-color-content" data-type="c3" data-val="{{automaticSeason.summerStartTime}}" bindtap="timeck">
            开始日期：{{automaticSeason.summerStartTime}}
            <van-icon name="arrow" />
          </view>
          <view class="flex-item text-color-content" data-type="c4" data-val="{{automaticSeason.summerEndTime}}" bindtap="timeck">
            结束日期：{{automaticSeason.summerEndTime}}
            <van-icon name="arrow" />
          </view>
        </view>
      </view>

      <!-- 分割线 -->
      <view style="border: 1px solid #f0f0f0;"></view>

      <!-- 秋季 -->
      <view class="padding-10">
        <view class="padding-left-5">秋季制冷</view>
        <view class="flex-row padding-top-10 padding-left-5">
          <view class="flex-item text-color-content" data-type="c5" data-val="{{automaticSeason.autumnStartTime}}" bindtap="timeck">
            开始日期：{{automaticSeason.autumnStartTime}}
            <van-icon name="arrow" />
          </view>
          <view class="flex-item text-color-content" data-type="c6" data-val="{{automaticSeason.autumnEndTime}}" bindtap="timeck">
            结束日期：{{automaticSeason.autumnEndTime}}
            <van-icon name="arrow" />
          </view>
        </view>
      </view>

      <!-- 分割线 -->
      <view style="border: 1px solid #f0f0f0;"></view>

      <!-- 冬季 -->
      <view class="padding-10">
        <view class="padding-left-5">冬季</view>
        <view class="flex-row padding-top-10 padding-left-5">
          <view class="flex-item text-color-content" data-type="c7" data-val="{{automaticSeason.winterStartTime}}" bindtap="timeck">
            开始日期：{{automaticSeason.winterStartTime}}
            <van-icon name="arrow" />
          </view>
          <view class="flex-item text-color-content" data-type="c8" data-val="{{automaticSeason.winterEndTime}}" bindtap="timeck">
            结束日期：{{automaticSeason.winterEndTime}}
            <van-icon name="arrow" />
          </view>
        </view>
      </view>
    </view>
    <van-cell title="工作状态" >
      <van-radio-group value="{{''+projectOverallSettings.workingCondition}}" direction="horizontal" bind:change="onWorkingCondition">
        <van-radio name="0">关闭</van-radio>
        <van-radio name="1">运行</van-radio>
        <!-- <van-radio name="2">模式</van-radio> -->
      </van-radio-group>
    </van-cell>
    <div class="flex-row padding-tb-10 bg-color-white" wx:if="{{projectOverallSettings.workingCondition==2}}"> 
      <van-radio-group value="{{''+projectOverallSettings.automaticWorking}}" direction="horizontal" bind:change="onAutomaticWorking">
        <van-radio name="1">值班</van-radio>
        <van-radio name="2">假期</van-radio>
      </van-radio-group>
    </div>
    <div wx:if="{{projectOverallSettings.automaticWorking==2}}"> 
     <van-cell title="开启时间" value="{{projectOverallSettings.automaticWorkingVacationStatusTime}}"  data-index="{{index}}" is-link bind:tap="ontimeVisible" />
     <van-cell title="关闭时间" value="{{projectOverallSettings.automaticWorkingVacationEndTime}}" data-index="{{index}}" is-link bind:tap="offtimeVisible"/>
    </div>
  </van-cell-group>

  <view class="padding-20">
  <van-button type="info" round block bind:click="save">保存</van-button>
</view>
</view>
<view wx:else=""></view>
   </view>
  <!-- 日期选择器 -->
  <van-popup show="{{showPicker}}" position="bottom">
    <van-picker show-toolbar columns="{{columns}}"  value="{{currentDate}}" bind:confirm="onConfirm" bind:cancel="onCancel" bind:change="onPickerChange" />
  </van-popup>
  <van-toast id="van-toast" />
  <van-dialog id="van-dialog" />