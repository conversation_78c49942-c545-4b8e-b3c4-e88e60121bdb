<!--pages/login/index.wxml-->
<view class="main-page-view" >
  <view class="flex-column" style="height: 80vh;" >
     <view class="margin-bottom-50"> 
       <view class="text-align-center text-color-primary text-font-bold text-font-50" style="letter-spacing: 1px;">Vivaldi</view> 
       <view class="padding-top-10 text-align-center">广州维瓦尔第五恒空调</view>
      </view>
     <view class="margin-top-50">
         <view class="flex-row padding-lr-10">
         <van-checkbox shape="square" icon-size="16px" value="{{checkboxagree}}" bind:change="checkboxChange"/>
         <view class="text-font-14">
            <text>我已经阅读并且同意</text>
            <text bindtap='agree' style="color: blue; text-decoration: underline;">《用户服务协议》</text>
            <text>和</text>
              <text  bindtap='privacypolicy' style="color: blue; text-decoration: underline;">《隐私政策》</text>
            </view>
          </view>
         <view class="padding-lr-50 padding-top-20">
          <van-button type="info" round blocktheme="primary" data-type="0" disabled="{{!checkboxagree}}" block open-type="getPhoneNumber" bindgetphonenumber="submit" >手机号快速登录</van-button> 
         </view>
         <!-- <view class="padding-lr-50 padding-top-20">
          <van-button  class="margin-top-10" type="info" data-type="1" round blocktheme="primary" disabled="{{!checkboxagree}}" block open-type="getPhoneNumber" bindgetphonenumber="submit" >经销商登录</van-button>
         </view> -->
         <view class="text-font-14 text-align-center text-color-content padding-top-20" bind:tap="back">暂不登录</view>
       </view>
  </view>
</view>
<van-toast id="van-toast" />
  <van-dialog id="van-dialog" />