const request = require('./request.js');
const app = getApp()
const getUserInfoAny = (callback,failback) => {
  wx.login({
    success: function (data) {
        request.get({
          url: 'SysUser/UserIsAny',
          data: {
            code: data.code
           },
           load:false
        })
        .then(res => {
            app.globalData.token = res.data.token
            wx.setStorage({
              key:"userInfo",
              data:res.data
            })           
            callback(res)         
        })
        .catch(err => {
            failback(err)
        });
      }
  })
}
//暴露接口供外部调用
module.exports = {
  getUserInfoAny
}