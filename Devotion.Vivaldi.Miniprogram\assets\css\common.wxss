/*2024-04-12 <PERSON><PERSON> */
.page {background-color: #EEF1F5;}
.page-view{  width: 95%; margin: 0 auto;padding: 20rpx 0px;}
.page-view-rows {background-color: white;width: 95%;margin: 0 auto;border-radius: 30rpx;padding: 20rpx; margin-bottom: 30rpx;}


.page-view .title{  font-size: 36rpx;  font-weight: bold;}
.page-view-item{line-height: 2em;padding: 0 20rpx;}
.page-view-item-c{  flex: 0.8;  padding-bottom: 10rpx;}
.page-view-item-r{  flex: 0.2;  text-align: right;}


.page-head{ width: 90%; margin: 0 auto;padding: 30rpx 0px;}
.page-head .title{font-size: 40rpx;font-weight: bold;}
.page-head .title{font-size: 40rpx;font-weight: bold;}
.page-head .page-view-item-r image{ width: 50rpx; height: 50rpx;}
.page-view .form{border-radius: 30rpx; background-color: white;padding: 30rpx 0px; }
.van-hairline--top-bottom:after {border-width:0 !important;border-bottom-width: 1px !important;}
.btn-bottom{ width: 60%; margin:0 auto;line-height: 80rpx !important; height: 80rpx !important;border: 0 !important;}
/* 正常蓝 */
.btn-blue{background-color: #338FFA  !important;color: #fff;}
.btn-grey{background-color: #ccc !important;}
/* 深红 */
.btn-red-deep{background-color: #BD3124  !important;color: #fff;}
/* 浅绿 */
.btn-green-shallow{background-color: #54BCBD !important;color: #fff;}
/* 透明按钮（隐藏按钮） */
.btn-transparent{background-color: inherit;}
.btn-transparent:after {  content: none;}
.btn-transparent::after {  border: none;}

/* vant自定义样式 */
.group-rows{margin-bottom: 30rpx;}
.van-cell-group_custom .van-cell{ line-height: 2.5em;}
.van-cell-group_custom .van-icon{ padding:0 10rpx ;}
.padding {text-align: center;}
.van-field__body{  line-height: 2.5em;height: 2.5em;}
.van-cell__right-icon-wrap {height: 2em !important;}