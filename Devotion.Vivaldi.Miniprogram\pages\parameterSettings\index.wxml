<!--pages/modeSwitching/index.wxml-->
<view class="padding-lr-10">
  <view class="padding-left-10 padding-tb-10 text-color-primary text-font-bold text-font-20">{{roomname}}</view>
</view>
<view class="text-align-center text-color-black  text-font-bold padding-bottom-10">房间上报状态</view>
<van-cell-group inset>
  <van-cell title="实际作用温度" value="{{info.currentTemperature}}°C" />
  <van-cell title="实际相对湿度" value="{{info.currentHumidity}}%" />
  <van-cell title="实际干球温度"  value="{{info.currentDryBulbTemperature}}%" ></van-cell>
  <van-cell title="实际露点" value="{{info.currentDewPoint}}" />
  <van-cell wx:for="{{info.thermoelectricValve}}"  wx:key="index" title="{{item.thermoelectricValveDO}}#热电阀状态" value="{{item.getThermalValveStatus?'开':'关'}}" />
  <van-cell wx:for="{{info.airValve}}"  wx:key="index" title="{{item.airValveDO}}#风阀状态" value="{{item.getAirValveStatus?'开':'关'}}" />
</van-cell-group>
<view class="text-align-center text-color-black padding-tb-10 text-font-bold padding-bottom-10">房间设置</view>
<van-cell-group inset>
  <van-cell  title="设定温度" value="{{info.setTemperature}}°C"  is-link value-class="text-color-content" />
     <view class="padding-tb-20 padding-lr-20 bg-color-white">
      <van-slider max='50' min='0' step='0.5' bar-height='10' value="{{info.setTemperature}}" bind:change="onslider"  bind:drag="ondrag" />
     </view>
     <van-cell title="设定湿度" value="{{info.setHumidity}}%"  is-link value-class="text-color-content" />
     <view class="padding-tb-20 padding-lr-20 bg-color-white">
      <van-slider max='100' min='0' step='0.5' bar-height='10' value="{{info.setHumidity}}" bind:change="onslider1"  bind:drag="ondrag1" />
     </view>
     <view wx:for="{{info.thermoelectricValve}}"  wx:key="index">  
     <van-cell  title="{{item.thermoelectricValveDO}}#热电阀状态"  >
      <van-radio-group value="{{''+item.setThermalValveStatus}}"   data-index="{{index}}"  direction ="horizontal"  bind:change="onValveStatusChange">
        <van-radio name="0">关闭</van-radio>
        <van-radio name="2">自动</van-radio>
      </van-radio-group> 
     </van-cell>
    </view>
    <view wx:for="{{info.airValve}}" wx:key="index">  
     <van-cell title="{{item.airValveDO}}#风阀状态" title-width="100px"  >
      <van-radio-group value="{{''+item.setAirValveStatus}}"  data-index="{{index}}"  direction ="horizontal" bind:change="onValveStatusChange1">
        <van-radio name="0">关闭</van-radio>
        <van-radio name="1">开启</van-radio>
        <van-radio name="2">自动</van-radio>
      </van-radio-group> 
     </van-cell>
     <div wx:if="{{item.setAirValveStatus===2}}"> 
     <van-cell title="开启时间" value="{{item.onAirValveTime}}"  data-index="{{index}}" is-link bind:tap="ontimeVisible" />
     <van-cell title="关闭时间" value="{{item.offAirValveTime}}" data-index="{{index}}" is-link bind:tap="offtimeVisible"/>
    </div>
    </view>
</van-cell-group>
<view class="padding-20">
  <van-button type="info" round block bind:click="onConfirmClick">保存</van-button>
</view>
<van-popup show="{{timeVisible}}" position="bottom"  round custom-style="height:40%;" bind:close="timeVisibleclos">
  <van-datetime-picker
  type="time"
  value="{{ currentDate }}"
  bind:cancel="timeVisibleclos"
  bind:confirm="timeVisibleconfirm"
/>
</van-popup>
<van-toast id="van-toast" />
<van-dialog id="van-dialog" />