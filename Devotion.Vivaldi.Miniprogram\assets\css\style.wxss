page {
  --primary:#1677ff;
  --success: #52c41a;
  --warning: #faad14;
  --error: #ff4d4f;
  --white: #FFFFFF;
  --gray:#d9d9d9;
  --textgray:#969799;
  --content:#515a6e;
  --black: #323233;
  --red: #f5222d;
  --volcano:#fa541c;
  --orange:#fa8c16;
  --gold:#faad14;
  --yellow:#fadb14;
  --lime: #a0d911;
  --green:#52c41a;
  --cyan:#13c2c2;
  --daybreakblue:#1677ff;
  --geekblue :#1d39c4;
  --purple:#722ed1;
  --magenta :#eb2f96;
  --shadowsize: 0px 0px 20rpx 4px;
}
/*==========字体风格================*/
/*大小*/
.text-font-10{font-size:20rpx !important}
.text-font-11{font-size:22rpx !important}
.text-font-12{font-size:24rpx !important}
.text-font-13{font-size:26rpx !important}
.text-font-14{font-size:28rpx !important}
.text-font-15{font-size:30rpx !important}
.text-font-16{font-size:32rpx !important}
.text-font-17{font-size:34rpx !important}
.text-font-18{font-size:36rpx !important}
.text-font-19{font-size:38rpx !important}
.text-font-20{font-size:40rpx !important}
.text-font-21{font-size:42rpx !important}
.text-font-22{font-size:44rpx !important}
.text-font-23{font-size:46rpx !important}
.text-font-24{font-size:48rpx !important}
.text-font-25{font-size:50rpx !important}
.text-font-26{font-size:52rpx !important}
.text-font-27{font-size:54rpx !important}
.text-font-28{font-size:56rpx !important}
.text-font-29{font-size:58rpx !important}
.text-font-30{font-size:60rpx !important}
.text-font-31{font-size:62rpx !important}
.text-font-32{font-size:64rpx !important}
.text-font-33{font-size:66rpx !important}
.text-font-34{font-size:68rpx !important}
.text-font-35{font-size:70rpx !important}
.text-font-36{font-size:72rpx !important}
.text-font-37{font-size:74rpx !important}
.text-font-38{font-size:76rpx !important}
.text-font-39{font-size:78rpx !important}
.text-font-40{font-size:80rpx !important}
.text-font-45{font-size:90rpx !important}
.text-font-50{font-size:100rpx !important}
/*粗细*/
.text-font-bold{font-weight: bold !important}
.text-font-bolder{font-weight:bolder !important}
.text-font-normal{font-weight:normal !important}
.text-font-lighter{font-weight:lighter !important}
/*位置*/
.text-align-center{text-align: center !important}
.text-align-left{text-align: left !important}
.text-align-right {text-align: right !important}
/*===============布局================*/
/* -- flex -- */
.flex-column{display: flex;flex-direction: column;align-items: center;justify-content: center}
.flex-row{display: flex;flex-direction: row;justify-content: center;align-items: center;}
.flex-item{flex: 1;}
.flex-item-1{flex: 0.1;}
.flex-item-2{flex: 0.2;}
.flex-item-3{flex: 0.3;}
.flex-item-4{flex: 0.4;}
.flex-item-5{flex: 0.5;}
.flex-item-6{flex: 0.6;}
.flex-item-7{flex: 0.7;}
.flex-item-8{flex: 0.8;}
.flex-item-9{flex: 0.9;}
/*颜色*/
.text-color-primary {color: var(--primary)!important}
.text-color-success {color: var(--success)!important}
.text-color-warning {color: var(--warning)!important}
.text-color-error {color: var(--error)!important}
.text-color-white {color: var(--white)!important}
.text-color-gray {color: var(--gray)!important}
.text-color-content {color: var(--content)!important}
.text-color-textgray{color: var(--textgray)!important}
.text-color-black {color: var(--black)!important}
.text-color-red {color: var(--red)!important}
.text-color-volcano {color: var(--volcano)!important}
.text-color-orange {color: var(--orange)!important}
.text-color-gold {color: var(--gold)!important}
.text-color-yellow {color: var(--yellow)!important}
.text-color-lime {color: var(--lime)!important}
.text-color-green {color: var(--green)!important}
.text-color-cyan {color: var(--cyan)!important}
.text-color-daybreakblue {color: var(--daybreakblue)!important}
.text-color-geekblue {color: var(--geekblue)!important}
.text-color-purple {color: var(--purple)!important}
.text-color-magenta {color: var(--magenta)!important}
/*==========背景================*/
.bg-color-primary {background-color: var(--primary)}
.bg-color-success {background-color: var(--success)}
.bg-color-warning {background-color: var(--warning)}
.bg-color-error {background-color: var(--error)}
.bg-color-white {background-color: var(--white)}
.bg-color-gray {background-color: var(--gray)}
.bg-color-content {background-color: var(--content)}
.bg-color-black {background-color: var(--black)}
.bg-color-red {background-color: var(--red)}
.bg-color-volcano {background-color: var(--volcano)}
.bg-color-orange {background-color: var(--orange)}
.bg-color-gold {background-color: var(--gold)}
.bg-color-yellow {background-color: var(--yellow)}
.bg-color-lime {background-color: var(--lime)}
.bg-color-green {background-color: var(--green)}
.bg-color-cyan {background-color: var(--cyan)}
.bg-color-daybreakblue {background-color: var(--daybreakblue)}
.bg-color-geekblue {background-color: var(--geekblue)}
.bg-color-purple {background-color: var(--purple)}
.bg-color-magenta {background-color: var(--magenta)}
/*===============边距================*/
/*内边距*/
.padding-5{padding:10rpx}
.padding-10{padding:20rpx}
.padding-20{padding:40rpx}
.padding-30{padding:60rpx}
.padding-40{padding:80rpx}
.padding-50{padding:100rpx}
.padding-left-5{padding-left:10rpx}
.padding-left-10{padding-left:20rpx}
.padding-left-20{padding-left:40rpx}
.padding-left-30{padding-left:60rpx}
.padding-left-40{padding-left:80rpx}
.padding-left-50{padding-left:100rpx}
.padding-right-2{padding-right:4rpx}
.padding-right-5{padding-right:10rpx}
.padding-right-10{padding-right:20rpx}
.padding-right-20{padding-right:40rpx}
.padding-right-30{padding-right:60rpx}
.padding-right-40{padding-right:80rpx}
.padding-right-50{padding-right:100rpx}
.padding-top-5{padding-top:10rpx}
.padding-top-10{padding-top:20rpx}
.padding-top-20{padding-top:40rpx}
.padding-top-30{padding-top:60rpx}
.padding-top-40{padding-top:80rpx}
.padding-top-50{padding-top:100rpx}
.padding-bottom-5{padding-bottom:10rpx}
.padding-bottom-10{padding-bottom:20rpx}
.padding-bottom-20{padding-bottom:40rpx}
.padding-bottom-30{padding-bottom:60rpx}
.padding-bottom-40{padding-bottom:80rpx}
.padding-bottom-50{padding-bottom:100rpx}
.padding-lr-5{padding-left:10rpx;padding-right:10rpx}
.padding-lr-10{padding-left:20rpx;padding-right:20rpx}
.padding-lr-20{padding-left:40rpx;padding-right:40rpx}
.padding-lr-30{padding-left:60rpx;padding-right:60rpx}
.padding-lr-40{padding-left:80rpx;padding-right:80rpx}
.padding-lr-50{padding-left:100rpx;padding-right:100rpx}
.padding-tb-5{padding-top:10rpx;padding-bottom:10rpx}
.padding-tb-10{padding-top:20rpx;padding-bottom:20rpx}
.padding-tb-20{padding-top:40rpx;padding-bottom:40rpx}
.padding-tb-30{padding-top:60rpx;padding-bottom:60rpx}
.padding-tb-40{padding-top:80rpx;padding-bottom:80rpx}
.padding-tb-50{padding-top:100rpx;padding-bottom:100rpx}
/*外边距*/
.margin-5{margin:10rpx}
.margin-10{margin:20rpx}
.margin-20{margin:40rpx}
.margin-30{margin:60rpx}
.margin-40{margin:80rpx}
.margin-50{margin:100rpx}
.margin-left-5{margin-left:10rpx}
.margin-left-10{margin-left:20rpx}
.margin-left-20{margin-left:40rpx}
.margin-left-30{margin-left:60rpx}
.margin-left-40{margin-left:80rpx}
.margin-left-50{margin-left:100rpx}
.margin-right-5{margin-right:10rpx}
.margin-right-10{margin-right:20rpx}
.margin-right-20{margin-right:40rpx}
.margin-right-30{margin-right:60rpx}
.margin-right-40{margin-right:80rpx}
.margin-right-50{margin-right:100rpx}
.margin-top-5{margin-top:10rpx}
.margin-top-10{margin-top:20rpx}
.margin-top-20{margin-top:40rpx}
.margin-top-30{margin-top:60rpx}
.margin-top-40{margin-top:80rpx}
.margin-top-50{margin-top:100rpx}
.margin-bottom-5{margin-bottom:10rpx}
.margin-bottom-10{margin-bottom:20rpx}
.margin-bottom-20{margin-bottom:40rpx}
.margin-bottom-30{margin-bottom:60rpx}
.margin-bottom-40{margin-bottom:80rpx}
.margin-bottom-50{margin-bottom:100rpx}
.margin-lr-5{margin-left:10rpx;margin-right:10rpx}
.margin-lr-10{margin-left:20rpx;margin-right:20rpx}
.margin-lr-20{margin-left:40rpx;margin-right:40rpx}
.margin-lr-30{margin-left:60rpx;margin-right:60rpx}
.margin-lr-40{margin-left:80rpx;margin-right:80rpx}
.margin-lr-50{margin-left:100rpx;margin-right:100rpx}
.margin-tb-5{margin-top:10rpx;margin-bottom:10rpx}
.margin-tb-10{margin-top:20rpx;margin-bottom:20rpx}
.margin-tb-20{margin-top:40rpx;margin-bottom:40rpx}
.margin-tb-30{margin-top:60rpx;margin-bottom:60rpx}
.margin-tb-40{margin-top:80rpx;margin-bottom:80rpx}
.margin-tb-50{margin-top:100rpx;margin-bottom:100rpx}
/* foot */
.foot {
  position: fixed;
  bottom: 50rpx;
  left: 0px;
  right: 0px;
    /* 适配手机底部黑条安全区域 */
    padding-bottom: constant(safe-area-inset-bottom); /*兼容 IOS<11.2*/
    padding-bottom: env(safe-area-inset-bottom); /*兼容 IOS>11.2*/
}
.foot-1 {
  position: fixed;
  bottom: 0px;
  left: 0px;
  right: 0px;
  /* 适配手机底部黑条安全区域 */
  padding-bottom: constant(safe-area-inset-bottom); /*兼容 IOS<11.2*/
  padding-bottom: env(safe-area-inset-bottom); /*兼容 IOS>11.2*/
}
.foot-Sticky  {
  background-color: #FFFFFF;
  position: fixed;
  bottom: 0px;
  left: 0px;
  right: 0px;
  padding: 35rpx 100rpx ;
  z-index: 999;
}