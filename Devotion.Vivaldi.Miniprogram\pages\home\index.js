// pages/home/<USER>
const request = require('../../utils/request.js');
const toastDialog = require('../../utils/toastDialog.js')
const app = getApp();
var that;
Page({
  /**
   * 页面的初始数据
   */
  data: {
    isnew:true, //判断是否新用户
    userInfo:null,
    clickProjectId: 0,
    projectName:'',
    projectlist:[],
    projectVisible:false,
    floorlist:[],
    clickFloor:0,
    roomlist:[],
    imageUrl: app.globalData.imageUrl,
  },

  //业主获取项目信息
  getListByUserId(){
    request.get({
      url: 'Project/GetListByUserId',
      data: {
        userId: that.data.userInfo.id
      }
    })
    .then(res => {
      var rdata = res.data
      if(rdata.length>0){
        that.setData({
          projectlist: rdata
        });
        var clickProjectId = that.data.clickProjectId
        var newData = rdata.filter((item) => item.id === clickProjectId)
        if(newData.length != 0){
              //项目还存在
             var projectName= newData[0].projectName
                    
              that.setData({
                clickProjectId: newData[0].id,
                projectName:projectName
              })
              wx.setStorage({
                key:"clickProjectId",
                data:newData[0].id
              })       
              wx.setStorage({
                key:"projectName",
                data:projectName
              }) 
             that.getFloorListByProjectId(newData[0].id)
             that.getSensorConfigByProjectIdAndFloor(newData[0].id)
        }else{
              //项目不存在，重新读取第一个
              var projectName= rdata[0].projectName
       
              that.setData({
                clickProjectId: rdata[0].id,
             
                projectName:projectName
      
              })
              wx.setStorage({
                key:"clickProjectId",
                data:rdata[0].id
              }) 
              wx.setStorage({
                key:"projectName",
                data:projectName
              }) 
              that.getFloorListByProjectId(rdata[0].id)
              that.getSensorConfigByProjectIdAndFloor(rdata[0].id)
        }
      }
    })
  },
  //更根据项目ID获取楼层信息
  getFloorListByProjectId(projectId){
    request.get({
      url: 'Project/GetFloorListByProjectId',
      data: {
        projectId
      }
    })
    .then(res => {
      that.setData({
        floorlist: res.data
      })
    })
  },
  //根据项目Id和楼层获取房间信息
  getSensorConfigByProjectIdAndFloor(projectId){
    request.get({
      url: 'Project/GetSensorConfigByProjectIdAndFloor',
      data: {
        projectId,
        floor:that.data.clickFloor
      }
    })
    .then(res => {
      that.setData({
        roomlist: res.data
      })
    })

  },
    //切换项目
    cutfamily() {
   
      that.setData({
        projectVisible: true
      })
    },
    //选择项目
    onProjectConfirm(e){
      var projectId = e.currentTarget.dataset.id;
      app.globalData.clickFloor=0
      var projectName =  e.currentTarget.dataset.projectname;
      wx.setStorage({
        key:"clickProjectId",
        data:projectId
      }) 
      wx.setStorage({
        key:"projectName",
        data:projectName
      }) 
      that.setData({
        clickProjectId:projectId,
        projectName:projectName,
        projectVisible:false
      })
      that.getFloorListByProjectId(projectId)
      that.getSensorConfigByProjectIdAndFloor(projectId)

    },
   //切换楼层
  deviceListByfloor(e) {
    var floor = e.target.id;
    floor = parseInt(floor)
    that.setData({
      clickFloor: floor
    })
    app.globalData.clickFloor=floor
    that.getSensorConfigByProjectIdAndFloor(that.data.clickProjectId)
  },
  goDetail(e){
    var id = e.currentTarget.dataset.id;
    var roomname = e.currentTarget.dataset.roomname;
    wx.navigateTo({
      url: '/pages/parameterSettings/index?id='+id+'&roomname='+roomname,
    })
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
     that=this
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },
  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    that = this
  var clickProjectId = wx.getStorageSync('clickProjectId')
    that.setData({
      clickProjectId:clickProjectId==""?0:clickProjectId,
      clickFloor: app.globalData.clickFloor,
      isnew:app.globalData.isnew,
      userInfo: wx.getStorageSync('userInfo'),
    })
    if(!that.data.isnew){

      that.getListByUserId()
    }

  },
  login(){
    wx.navigateTo({
      url: '/pages/login/index?projectId=0'
    })
  },
  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  // onShareAppMessage() {

  // }
  onClose(){
    this.setData({
      projectVisible:false
    })

  }
})