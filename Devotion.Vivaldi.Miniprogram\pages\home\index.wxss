/* pages/home/<USER>/
.swiperimg-view-home {
  font-size: 31rpx;
}
.top{
  padding-top:20rpx;
  padding-left: 30rpx;
  padding-right: 30rpx;
  padding-bottom:20rpx;
  background-color: #EEF1F5;
  height: 240rpx;
}
/* 天气 */
.box{ 
  height: 150rpx;
  display: flex;flex-direction: row;justify-content: center;align-items: center;
}
.weather{
  display: flex;
  flex-direction: row;
  align-items: center;
  color: rgba(108,108,108,1);
  font-weight: bold;
  
}
.icon{
  width: 85rpx;
  height: 85rpx;
}
.menu {
  width: auto;
  white-space: nowrap;
  overflow-x: scroll;
  font-size: 30rpx;
}

.menu .menu_pa {
  border-radius: 5px;
  height: auto;
}

.menu_item {
  margin: 0 25rpx;
  width: auto;
  text-align: center;
}

.color_onClick {
  display: inline-block;
  font-weight: 600;
  color: #0081ff;
}

.color_onBlur {
  display: inline-block;
}


/* 房间 */
.room-page{
  width: 100%;
  overflow: hidden; /* 清除浮动 */
}
.clearfix::after {
  content: "";
  display: table;
  clear: both;
}
.room-page-view{
  float: left;
  height: 250rpx;
  width: calc(50% - 60rpx); /* 每个div占50%的宽度，减去间隔 */
  margin: 20rpx 30rpx; /* 每个div的间隔 */
  background-color: rgb(255, 255, 255); /* 背景颜色 */
  padding: 10rpx;
  border-radius: 30rpx;
  box-sizing: border-box;
}
.room-flex{
  display: flex;flex-direction: row;justify-content: center;align-items: center;
}
.room-page-column{
  display: flex;flex-direction: column;justify-content: center ;
  height: 100%;  
  position:relative
}
.room-page-column-item{
  width: 100%;
}
.flex-icon{
  width:80rpx;
  height:80rpx;
}
.floorclas{
  position: absolute;
  top: 20rpx;
  right: 50rpx;
}
.newview{
  background-color: white;
  height: 65vh;
  width: 100%;
  border-radius: 30rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;

}